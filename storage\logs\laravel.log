[2025-05-30 10:24:31] local.INFO: data is {"task_id":35,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:24:28","send_to_number":"919510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"573a1c41-5bb0-45a7-b709-564302e45f9f","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:24:31] local.DEBUG: {"message":"OK","referenceID":"86ee36f9-a737-4256-a128-8a3b2239e480"}  
[2025-05-30 10:24:31] local.DEBUG: Message Data: {"messageID":"573a1c41-5bb0-45a7-b709-564302e45f9f","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["919510991141"],"data":[{"content":{"plainText":"Hellow Template Desctiption Test"}}],"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 10:27:22] local.INFO: data is {"task_id":36,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:27:22","send_to_number":"919510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"229863b5-a90b-4b04-9c99-521c3ace89a2","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:27:23] local.DEBUG: {"message":"OK","referenceID":"cf170cfa-2028-458f-b8bc-f325de0d7e74"}  
[2025-05-30 10:27:23] local.DEBUG: Message Data: {"messageID":"229863b5-a90b-4b04-9c99-521c3ace89a2","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["919510991141"],"data":{"content":{"plainText":"Hellow Template Desctiption Test"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 10:29:37] local.INFO: data is {"task_id":37,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:29:32","send_to_number":"9510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"844e6b6c-a67d-498a-a145-f1a102d317ae","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:29:38] local.DEBUG: {"message":"OK","referenceID":"b8926956-25d3-4065-b88b-18d5e671ac62"}  
[2025-05-30 10:29:38] local.DEBUG: Message Data: {"messageID":"844e6b6c-a67d-498a-a145-f1a102d317ae","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Hellow Template Desctiption Test"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 10:29:38] local.INFO: data is {"task_id":38,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:29:32","send_to_number":"8320677031","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"7821d0b1-e2e1-4d62-8734-a5a1c60be04e","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:29:38] local.DEBUG: {"message":"OK","referenceID":"7e8f29c1-2bcd-40aa-a7cd-239c1357c50f"}  
[2025-05-30 10:29:38] local.DEBUG: Message Data: {"messageID":"7821d0b1-e2e1-4d62-8734-a5a1c60be04e","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["8320677031"],"data":{"content":{"plainText":"Hellow Template Desctiption Test"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 10:30:30] local.INFO: data is {"task_id":39,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:30:26","send_to_number":"9510991141","templateId":"10","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"784fc582-5d3a-47f5-85c9-fe8cb5047d3a","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:30:30] local.DEBUG: {"message":"OK","referenceID":"9121b118-0437-477d-997a-83ec7a5204dc"}  
[2025-05-30 10:30:30] local.DEBUG: Message Data: {"messageID":"784fc582-5d3a-47f5-85c9-fe8cb5047d3a","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"HORIZONTAL","content":{"cardTitle":"Rich Card","cardDescription":"Rich Card Description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/17485802362.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/17485802362.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748580237.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Reply","postBack":{"data":"reply_suggestion_1"}}},{"action":{"plainText":"Call Us","postBack":{"data":"dial_suggestion_2"},"dialerAction":{"phoneNumber":"+91 8320677031"}}},{"action":{"plainText":"Click Here","postBack":{"data":"url_suggestion_3"},"openUrl":{"url":"www.google.com"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 10:39:06] local.INFO: RCS File Upload Response {"status":200,"body":"{\"fileId\":\"68393d726a70e24038e75172\",\"fileUrl\":\"https://jfxv.akamaized.net/PublicStorage/Bot/6827413e294fdad2ed772d65/1748581745105.png\",\"status\":\"success\"}","file_name":"NXC_new_logo_white.png","file_size":41913,"agent_id":"0b6345e5-710a-438f-af94-fd810d025250"} 
[2025-05-30 10:39:43] local.INFO: data is {"task_id":40,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:39:42","send_to_number":"9510991141","templateId":"11","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"51a0b021-9c2d-4ee6-92e6-b208d71b5709","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:39:44] local.DEBUG: {"message":"OK","referenceID":"44815832-6c8b-4fc2-b4d4-fb201b3e2eef"}  
[2025-05-30 10:39:44] local.DEBUG: Message Data: {"messageID":"51a0b021-9c2d-4ee6-92e6-b208d71b5709","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"VERTICAL","content":{"cardTitle":"Card Title","cardDescription":"Card Description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748581745105.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748581745105.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748581746.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"testingt t","postBack":{"data":"reply_suggestion_1"}}},{"action":{"plainText":"Visit Now","postBack":{"data":"url_suggestion_2"},"openUrl":{"url":"www.google.com"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 10:40:59] local.INFO: data is {"task_id":41,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:40:58","send_to_number":"9510991141","templateId":"11","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"239fc1b8-fccf-4a27-9670-89fafc58a376","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:41:00] local.DEBUG: {"message":"OK","referenceID":"260fc7e3-6a87-4efa-a233-0c662e6579ea"}  
[2025-05-30 10:41:00] local.DEBUG: Message Data: {"messageID":"239fc1b8-fccf-4a27-9670-89fafc58a376","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"VERTICAL","content":{"cardTitle":"card title","cardDescription":"description","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/66e025b800adf197e64fffbc\/1727684177481.png"}},"suggestions":[{"action":{"plainText":"Visit Now","postBack":{"data":"visit_now_election24{{$50}}"},"openUrl":{"url":"https:\/\/elections24.eci.gov.in\/"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 10:49:22] local.INFO: data is {"task_id":42,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:49:19","send_to_number":"9510991141","templateId":"8","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"53454a25-7c90-4150-8912-d5ef7877ebbd","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:50:43] local.INFO: data is {"task_id":43,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:50:41","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"a2bdc1e0-f068-4aff-ba97-0469f3f9b75f","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:51:27] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-05-30 10:51:44] local.INFO: data is {"task_id":44,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:51:42","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"19dfb9d9-ff17-4a25-8f07-8fc40539ceb6","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:52:00] local.INFO: data is {"task_id":45,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:51:59","send_to_number":"95410991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"fb84a57f-97da-4ce0-a3f7-c9a4c37a53ca","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 10:54:41] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-05-30 10:54:41] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-05-30 10:54:41] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-05-30 10:55:05] local.INFO: data is {"task_id":46,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 10:55:04","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"5a24ac39-b121-4591-b54d-dde54b10ded3","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:42:47] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-05-30 11:43:05] local.INFO: data is {"task_id":47,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:43:05","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"9caf3cac-8f0b-43cb-a514-321b2fef7cd6","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:43:05] local.DEBUG: {"message":"OK","referenceID":"8de37bed-2431-42eb-b130-bc92717de37a"}  
[2025-05-30 11:43:05] local.DEBUG: Message Data: {"messageID":"9caf3cac-8f0b-43cb-a514-321b2fef7cd6","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"atesteasdfs"},"local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748325372.jpg","upload_error":"RCS API credentials not configured"},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:43:05] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 11:43:44] local.INFO: data is {"task_id":48,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:43:41","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"b67decf5-755f-4179-89af-5c058966488e","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:43:44] local.DEBUG: {"message":"OK","referenceID":"5c83c691-0ba9-440d-8f4e-a4700ca3be67"}  
[2025-05-30 11:43:44] local.DEBUG: Message Data: {"messageID":"b67decf5-755f-4179-89af-5c058966488e","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"atesteasdfs"},"local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748325372.jpg","upload_error":"RCS API credentials not configured"},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:43:44] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 11:47:14] local.ERROR: App\Jobs\CheckCapability has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\CheckCapability has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-05-30 11:47:14] local.ERROR: App\Jobs\CheckCapability has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\CheckCapability has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-05-30 11:47:26] local.INFO: data is {"task_id":49,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:47:24","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"f9bb9cbc-13c7-4cae-8762-3508d8dbd86c","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:47:26] local.DEBUG: {"message":"OK","referenceID":"c4d46969-4876-4551-803c-c1c6b7d3de5e"}  
[2025-05-30 11:47:26] local.DEBUG: Message Data: {"messageID":"f9bb9cbc-13c7-4cae-8762-3508d8dbd86c","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"atesteasdfs"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:47:26] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 11:47:27] local.DEBUG: [true]  
[2025-05-30 11:49:29] local.INFO: data is {"task_id":50,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:49:28","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"935f5f4b-7b06-4e9e-aa8d-e8539939a076","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:49:30] local.DEBUG: {"message":"OK","referenceID":"0a8acbb1-b988-4de6-af16-32f36691fb8e"}  
[2025-05-30 11:49:30] local.DEBUG: Message Data: {"messageID":"935f5f4b-7b06-4e9e-aa8d-e8539939a076","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"atesteasdfs"},"local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748325372.jpg","upload_error":"RCS API credentials not configured"},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:49:30] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 11:49:30] local.INFO: Number 9510991141 is capable.  
[2025-05-30 11:50:35] local.INFO: data is {"task_id":51,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:50:33","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"6ce9e8cd-4ae9-4e70-a747-38c888dcf11f","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:50:36] local.DEBUG: {"message":"OK","referenceID":"58f8b56f-bfc7-45c9-9942-0117546c4ec6"}  
[2025-05-30 11:50:36] local.DEBUG: Message Data: {"messageID":"6ce9e8cd-4ae9-4e70-a747-38c888dcf11f","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"atesteasdfs"},"local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748325372.jpg","upload_error":"RCS API credentials not configured"},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:50:36] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 11:50:37] local.INFO: Number 9510991141 is capable.  
[2025-05-30 11:51:07] local.INFO: data is {"task_id":52,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:51:05","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"352a8666-d828-4c14-b80a-d5e5a48b6509","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:51:07] local.DEBUG: {"message":"OK","referenceID":"1fa9902b-2699-48ce-a08c-a7ca852ed170"}  
[2025-05-30 11:51:07] local.DEBUG: Message Data: {"messageID":"352a8666-d828-4c14-b80a-d5e5a48b6509","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"atesteasdfs"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:51:07] local.INFO: data is {"task_id":53,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:51:05","send_to_number":"8320677031","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"5178c03b-4678-439e-9e34-e82fc3a88c8f","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:51:08] local.DEBUG: {"message":"OK","referenceID":"84e3cda3-1556-4cb0-bc52-f0c2ab978565"}  
[2025-05-30 11:51:08] local.DEBUG: Message Data: {"messageID":"5178c03b-4678-439e-9e34-e82fc3a88c8f","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["8320677031"],"data":{"content":{"plainText":"atesteasdfs"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:51:08] local.INFO: data is {"task_id":54,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 11:51:05","send_to_number":"9484403354","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"0241f0f7-f8ac-4043-abee-443ceff2219f","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 11:51:08] local.DEBUG: {"message":"OK","referenceID":"92c31db2-9828-43ca-bf85-987e069455eb"}  
[2025-05-30 11:51:08] local.DEBUG: Message Data: {"messageID":"0241f0f7-f8ac-4043-abee-443ceff2219f","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9484403354"],"data":{"content":{"plainText":"atesteasdfs"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 11:51:08] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 11:51:09] local.INFO: Number 9510991141 is capable.  
[2025-05-30 11:51:09] local.INFO: Inside job handle for number: 8320677031  
[2025-05-30 11:51:10] local.INFO: Number 8320677031 is not capable.  
[2025-05-30 11:51:10] local.INFO: Inside job handle for number: 9484403354  
[2025-05-30 11:51:10] local.INFO: Number 9484403354 is capable.  
[2025-05-30 14:30:04] local.INFO: data is {"task_id":55,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 14:30:03","send_to_number":"9510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"959a1cc3-8f05-43e4-a4f4-df7b91cbbec8","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 14:30:05] local.DEBUG: {"message":"OK","referenceID":"82d7ba07-8288-4bce-89e4-73c2d58f318b"}  
[2025-05-30 14:30:05] local.DEBUG: Message Data: {"messageID":"959a1cc3-8f05-43e4-a4f4-df7b91cbbec8","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Hellow Template Desctiption Test"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 14:30:05] local.ERROR: Attempt to read property "message" on array {"stacktrace":"#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\Users\\\\<USER>\\\\...', 192)
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Traits\\Whatsapp.php(192): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\Users\\\\<USER>\\\\...', 192)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Jobs\\SendTaskJob.php(45): App\\Jobs\\SendTaskJob->sendwhatsapp(Array, NULL)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(30): App\\Jobs\\SendTaskJob->App\\Jobs\\{closure}(Object(Illuminate\\Database\\MySqlConnection))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(509): Illuminate\\Database\\Connection->transaction(Object(Closure))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Jobs\\SendTaskJob.php(38): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Jobs\\SendTaskJob->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\SendTaskJob))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\SendTaskJob))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(123): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\SendTaskJob), false)
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\SendTaskJob))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\SendTaskJob))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(122): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\SendTaskJob))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 {main}","request":""} 
[2025-05-30 14:30:05] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 14:30:05] local.INFO: Number 9510991141 is capable.  
[2025-05-30 14:31:25] local.INFO: data is {"task_id":56,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 14:31:22","send_to_number":"9510991141","templateId":"8","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"a50d4fe8-e042-45d7-9aa6-8ae15a7da341","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 14:31:26] local.DEBUG: {"message":"OK","referenceID":"04ae1fe0-e8c1-4ceb-b2b3-78241546fe6f"}  
[2025-05-30 14:31:26] local.DEBUG: Message Data: {"messageID":"a50d4fe8-e042-45d7-9aa6-8ae15a7da341","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"richCardDetails":{"standalone":{"cardOrientation":"HORIZONTAL","content":{"cardTitle":"asdf","cardDescription":"sadfasdfsadf","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748434217981.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748434217981.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748434217.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"asdfasd","postBack":{"data":"reply_suggestion_1"}}},{"action":{"plainText":"sadfsad","postBack":{"data":"dial_suggestion_2"},"dialerAction":{"phoneNumber":"146546541"}}}]}}}}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 14:31:26] local.INFO: Message send okay with04ae1fe0-e8c1-4ceb-b2b3-78241546fe6f  
[2025-05-30 14:31:26] local.INFO: Inside job handle for number: 9510991141  
[2025-05-30 14:31:26] local.INFO: Number 9510991141 is capable.  
[2025-05-30 14:43:04] local.INFO: data is {"task_id":57,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 14:43:02","send_to_number":"9510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"a1540d9b-0fbb-4b38-8877-b294ce7a33c3","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 14:43:05] local.DEBUG: {"message":"OK","referenceID":"865d4622-4671-4483-9d72-39eeab268c0e"}  
[2025-05-30 14:43:05] local.DEBUG: Message Data: {"messageID":"a1540d9b-0fbb-4b38-8877-b294ce7a33c3","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Hellow Template Desctiption Test"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 14:43:05] local.INFO: Message send okay with865d4622-4671-4483-9d72-39eeab268c0e  
[2025-05-30 14:43:05] local.ERROR: Array to string conversion {"stacktrace":"#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'C:\\\\Users\\\\<USER>\\\\...', 194)
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Traits\\Whatsapp.php(194): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Array to string...', 'C:\\\\Users\\\\<USER>\\\\...', 194)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Jobs\\SendTaskJob.php(45): App\\Jobs\\SendTaskJob->sendwhatsapp(Array, NULL)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(30): App\\Jobs\\SendTaskJob->App\\Jobs\\{closure}(Object(Illuminate\\Database\\MySqlConnection))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(509): Illuminate\\Database\\Connection->transaction(Object(Closure))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Jobs\\SendTaskJob.php(38): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Jobs\\SendTaskJob->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\SendTaskJob))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\SendTaskJob))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(123): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\SendTaskJob), false)
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\SendTaskJob))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\SendTaskJob))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(122): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\SendTaskJob))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 {main}","request":""} 
[2025-05-30 14:44:51] local.INFO: data is {"task_id":58,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 14:44:48","send_to_number":"9510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"9148c23e-8c3e-4aab-8ab7-ea49d11fe7e0","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 14:44:52] local.DEBUG: {"message":"OK","referenceID":"4908668d-bf1a-49be-a8fc-ae279b68e4e5"}  
[2025-05-30 14:44:52] local.DEBUG: Message Data: {"messageID":"9148c23e-8c3e-4aab-8ab7-ea49d11fe7e0","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Hellow Template Desctiption Test"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 14:44:52] local.INFO: Message send okay with4908668d-bf1a-49be-a8fc-ae279b68e4e5  
[2025-05-30 14:47:05] local.INFO: data is {"task_id":59,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 14:47:04","send_to_number":"9510991141","templateId":"9","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"e6f7ab58-e6d2-424a-ae9f-9ab708d4214d","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 14:47:06] local.DEBUG: {"message":"OK","referenceID":"19c8201a-a9c3-439a-b8d7-5486f7a3b499"}  
[2025-05-30 14:47:06] local.DEBUG: Message Data: {"messageID":"e6f7ab58-e6d2-424a-ae9f-9ab708d4214d","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Hellow Template Desctiption Test"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 14:47:06] local.INFO: Message send okay with19c8201a-a9c3-439a-b8d7-5486f7a3b499  
[2025-05-30 16:07:23] local.INFO: data is {"task_id":60,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:07:22","send_to_number":"9510991141","templateId":"13","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"11556fe6-0f4e-461d-b70e-8f69e261add5","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 16:07:24] local.DEBUG: {"message":"OK","referenceID":"65ef183e-6beb-4577-8719-718675281e7a"}  
[2025-05-30 16:07:24] local.DEBUG: Message Data: {"messageID":"11556fe6-0f4e-461d-b70e-8f69e261add5","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Testing Description","suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"url_suggestion_1"},"openUrl":{"url":"www.gogle.com"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-05-30 16:07:24] local.INFO: Message send okay with65ef183e-6beb-4577-8719-718675281e7a  
[2025-05-30 16:11:31] local.INFO: data is {"task_id":61,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:11:30","send_to_number":"9510991141","templateId":"13","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"00172d05-a535-4c26-8751-073f107e1ed2","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 16:11:32] local.DEBUG: {"message":"OK","referenceID":"a24a4e30-1444-4b20-9fe5-17a2bba65a26"}  
[2025-05-30 16:11:32] local.DEBUG: Message Data: {"messageID":"00172d05-a535-4c26-8751-073f107e1ed2","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Testing Description","suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"url_suggestion_1"},"openUrl":{"url":"www.gogle.com"}}}]}}}  
[2025-05-30 16:11:32] local.INFO: Message send okay witha24a4e30-1444-4b20-9fe5-17a2bba65a26  
[2025-05-30 16:14:36] local.INFO: data is {"task_id":62,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:14:34","send_to_number":"9510991141","templateId":"13","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"d4a8a839-e8f5-4e46-907e-1bef30c5078c","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 16:14:36] local.DEBUG: {"message":"OK","referenceID":"3ef782a6-96fd-4a5b-8604-b8a497b597a4"}  
[2025-05-30 16:14:36] local.DEBUG: Message Data: {"messageID":"d4a8a839-e8f5-4e46-907e-1bef30c5078c","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Hi, Welcome to NXC Controls Pvt Ltd.\ud83e\udd73\ud83c\udf89","suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"call_back_data_for_button_1_goes_here{{$50}}"},"openUrl":{"url":"https:\/\/www.google.in\/"}}}]}}}  
[2025-05-30 16:14:36] local.INFO: Message send okay with3ef782a6-96fd-4a5b-8604-b8a497b597a4  
[2025-05-30 16:17:49] local.INFO: data is {"task_id":63,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:17:48","send_to_number":"9510991141","templateId":"13","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"185b9fa4-7ae1-411c-b48c-7296fb08ad32","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 16:17:50] local.DEBUG: {"message":"OK","referenceID":"fc78257c-69b9-4294-b227-dc77fd24f443"}  
[2025-05-30 16:17:50] local.DEBUG: Message Data: {"messageID":"185b9fa4-7ae1-411c-b48c-7296fb08ad32","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Hi, Welcome to NXC Controls Pvt Ltd.\ud83e\udd73\ud83c\udf89","suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"call_back_data_for_button_1_goes_here{{$50}}"},"openUrl":{"url":"https:\/\/www.google.in\/"}}}]}}}  
[2025-05-30 16:17:50] local.INFO: Message send okay withfc78257c-69b9-4294-b227-dc77fd24f443  
[2025-05-30 16:18:41] local.INFO: data is {"task_id":64,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:18:38","send_to_number":"9510991141","templateId":"14","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"f4443b79-3611-44ef-9361-8ecc807bd1e7","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 16:18:42] local.DEBUG: {"message":"OK","referenceID":"aab0ae03-bb15-4b11-8283-9476d7facd4d"}  
[2025-05-30 16:18:42] local.DEBUG: Message Data: {"messageID":"f4443b79-3611-44ef-9361-8ecc807bd1e7","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Testing Description","suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"url_suggestion_1"},"openUrl":{"url":"www.gogle.com"}}}]}}}  
[2025-05-30 16:18:42] local.INFO: Message send okay withaab0ae03-bb15-4b11-8283-9476d7facd4d  
[2025-05-30 16:20:28] local.INFO: data is {"task_id":65,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:20:25","send_to_number":"9510991141","templateId":"14","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"1f90204f-c3e6-45a5-bf94-99b227794945","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 16:20:28] local.DEBUG: {"message":"OK","referenceID":"9e928abd-21b1-4b8d-b777-63686ea60a83"}  
[2025-05-30 16:20:28] local.DEBUG: Message Data: {"messageID":"1f90204f-c3e6-45a5-bf94-99b227794945","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Testing Description","suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"url_suggestion_1"},"openUrl":{"url":"https:\/\/www.google.in\/"}}}]}}}  
[2025-05-30 16:20:28] local.INFO: Message send okay with9e928abd-21b1-4b8d-b777-63686ea60a83  
[2025-05-30 16:22:59] local.INFO: data is {"task_id":66,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-05-30 16:22:56","send_to_number":"9510991141","templateId":"14","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"fce5618c-bc49-49c0-aa0b-4a93fa0f99b4","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-05-30 16:22:59] local.DEBUG: {"message":"OK","referenceID":"b4c81389-96b3-4e86-8d0b-fa9c97e77a0c"}  
[2025-05-30 16:22:59] local.DEBUG: Message Data: {"messageID":"fce5618c-bc49-49c0-aa0b-4a93fa0f99b4","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Testing Description","suggestions":[{"action":{"plainText":"Pay Now","postBack":{"data":"call_back_data_for_button_1_goes_here"},"openUrl":{"url":"https:\/\/www.google.in\/"}}}]}}}  
[2025-05-30 16:22:59] local.INFO: Message send okay withb4c81389-96b3-4e86-8d0b-fa9c97e77a0c  
