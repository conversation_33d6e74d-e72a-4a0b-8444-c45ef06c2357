<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\TutorialVideo;
use Illuminate\Http\Request;
use Http;
use App\Models\Template;
use App\Models\Device;
use App\Traits\Whatsapp;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use App\Rules\Phone;
use Illuminate\Support\Facades\Auth;
use Response;
use App\Services\RcsApiService;

class TemplateController extends Controller
{

    use Whatsapp;

    //return template requeest form page
    public function index(Request $request)
    {
        $devices = Device::where('user_id', Auth::id())->withCount('smstransaction')->latest()->paginate(20);
        $device_id = Device::where('user_id', Auth::id())->pluck('id')->toArray();
        $templates = Template::where('user_id', Auth::id())
            ->whereIn('device_id', $device_id)
            ->where('status', 1)
            ->get();

        $total_templates = $templates->count();

        $videoTutorial = TutorialVideo::where('key', 'my_template')->first();
        return view('user.template.index', compact('devices', 'templates', 'total_templates', 'videoTutorial'));
    }

    public function view(Request $request)
    {
        return view('user.template.view');
    }

    public function create($device_uuid)
    {
        $device = Device::where('user_id', Auth::id())->where('uuid', $device_uuid)->first();

        return view('user.template.create', compact('device'));
    }

    public function store(Request $request, $type = null)
    {
        if (getUserPlanData('template_limit') == false) {
            return response()->json([
                'message' => __('Maximum Template Limit Exceeded')
            ], 401);
        }

        // Determine template type from form data if not provided in URL
        if ($type === null) {
            $templateType = $request->input('template_type', '1');
        } else {
            $templateType = $type;
        }

        // Convert numeric template type to string format
        $typeMapping = [
            '1' => 'plain-text',
            '2' => 'text-with-media',
            '4' => 'rich-card',
            '5' => 'rich-card-carousel'
        ];

        $templateTypeString = $typeMapping[$templateType] ?? 'plain-text';

        // Basic validation
        $validated = $request->validate([
            'template_name' => 'required|max:100',
        ]);

        // Type-specific validation
        if ($templateTypeString == 'text-with-media') {
            $request->validate([
                'media_file' => 'required|mimes:jpg,jpeg,png,webp,gif,mp4,3gp|max:5120', // 5MB max
                'template_text' => 'required|max:1000',
            ]);
        } elseif ($templateTypeString == 'rich-card') {
            $request->validate([
                'card_title' => 'required|max:100',
                'card_description' => 'required|max:1000',
                'card_media_file' => 'required|mimes:jpg,jpeg,png,webp,gif,mp4,3gp|max:5120',
            ]);
        } elseif ($templateTypeString == 'rich-card-carousel') {
            $request->validate([
                'card1_title' => 'required|max:100',
                'card1_description' => 'required|max:1000',
                'card1_media_file' => 'required|mimes:jpg,jpeg,png,webp,gif,mp4,3gp|max:5120',
                'card2_title' => 'required|max:100',
                'card2_description' => 'required|max:1000',
                'card2_media_file' => 'required|mimes:jpg,jpeg,png,webp,gif,mp4,3gp|max:5120',
            ]);
        } else {
            $request->validate([
                'template_text' => 'required|max:1000',
            ]);
        }

        // Convert form data to RCS API JSON format
        $rcsJsonData = $this->convertToRcsApiFormat($request, $templateTypeString);

        // Save template
        $template = new Template();
        $template->title = $request->template_name;
        $template->user_id = Auth::id();
        $template->device_id = $request->device_id;
        $template->body = $rcsJsonData;
        $template->type = $templateTypeString;
        $template->save();

        return response()->json([
            'message' => __('Template created successfully..!!'),
        ], 200);
    }

    public function edit($id)
    {
        $template = Template::where('user_id', Auth::id())->findorFail($id);

        if ($template->type == 'text-with-media') {
            $component = 'user.template.edit.media';
        } elseif ($template->type == 'text-with-location') {
            $component = 'user.template.edit.location';
        } elseif ($template->type == 'text-with-button') {
            $component = 'user.template.edit.button';
        } elseif ($template->type == 'text-with-template') {
            $component = 'user.template.edit.template';
        } elseif ($template->type == 'text-with-list') {
            $component = 'user.template.edit.list';
        } else {
            $component = 'user.template.edit.plaintext';
        }

        return view('user.template.edit', compact('template', 'component'));
    }

    public function update(Request $request, $id)
    {

        $template = Template::where('user_id', Auth::id())->findorFail($id);
        $type = $template->type;

        $validated = $request->validate([
            'template_name' => 'required|max:100',
        ]);

        if ($type == 'text-with-media') {
            $validated = $request->validate([
                'file' => 'mimes:jpg,jpeg,png,webp,gif,pdf,docx,xlsx,csv,txt|max:1000',
                'message' => 'required|max:1000',
            ]);

            if ($request->hasFile('file')) {
                $file = $this->saveFile($request, 'file');
                $exists_file = '';

                if (isset($template->body['image'])) {
                    $exists_file = $template->body['image']['url'];
                } elseif (isset($template->body['attachment'])) {
                    $exists_file = $template->body['attachment']['url'];
                }



                if ($exists_file != '') {
                    $fileArr = explode('uploads', $exists_file);
                    if (isset($fileArr[1])) {
                        $exists_file = 'uploads' . $fileArr[1];
                        if (Storage::exists($exists_file)) {
                            Storage::delete($exists_file);
                        }
                    }
                }
            } else {
                if (isset($template->body['image'])) {
                    $file = $template->body['image']['url'];
                } elseif (isset($template->body['attachment'])) {
                    $file = $template->body['attachment']['url'];
                }
            }

            $request['attachment'] = $file;
        } elseif ($type == 'text-with-vcard') {
            $validated = $request->validate([
                'display_name' => 'required|max:100',
                'full_name' => 'required|max:100',
                'org_name' => 'required|max:100',
                'contact_number' => ['required', new Phone, 'max:20'],
                'wa_number' => ['required', new Phone, 'max:20'],

            ]);
        } elseif ($type == 'text-with-button') {
            $validated = $request->validate([
                'footer_text' => 'required|max:100',
                'buttons.*' => 'required|max:50',
                'message' => 'required|max:1000',
            ]);

            if (count($request->buttons) > 3) {
                return response()->json([
                    'message' => __('Maximum Button Limit Is 3'),
                ], 403);
            }
        } elseif ($type == 'text-with-template') {
            $validated = $request->validate([
                'footer_text' => 'required|max:100',
                'buttons.*' => 'required|max:50',
                'message' => 'required|max:1000',
            ]);

            if (count($request->buttons) > 3) {
                return response()->json([
                    'message' => __('Maximum Button Limit Is 3'),
                ], 403);
            }

            $is_valid = true;
            $error_message = __('Please Follow the site rules');
            $types = ['urlButton', 'callButton', 'quickReplyButton'];
            $properties = ['displaytext', 'action', 'type'];

            foreach ($request->buttons as $key => $button) {
                if (count($button) < 3) {
                    $is_valid = false;
                    break;
                } else {


                    foreach ($button as $buttonKey => $buttonValue) {
                        if ($buttonKey == 'type') {
                            if (!in_array($buttonValue, $types)) {
                                $is_valid = false;
                                break;
                            }
                        }

                        if (!in_array($buttonKey, $properties)) {
                            $is_valid = false;
                            break;
                        } else {


                            if ($buttonKey == 'action') {

                                if (!empty($buttonValue)) {
                                    if (strlen($buttonValue) > 50) {
                                        $error_message = __('Maximum Button Value Limit 50');
                                        $is_valid = false;
                                    }
                                }
                                if ($button['type'] != 'quickReplyButton') {
                                    if (empty($buttonValue)) {

                                        $error_message = __('fill up all the fields');
                                        $is_valid = false;
                                    }
                                }
                            } else {


                                if (empty($buttonValue) || $buttonValue == null) {

                                    $error_message = __('fill up all the fields');
                                    $is_valid = false;
                                    break;
                                } else {
                                    if (strlen($buttonValue) > 50) {
                                        $error_message = __('Maximum Button Value Limit 50');
                                        $is_valid = false;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if ($is_valid == false) {
                return response()->json([
                    'message' => $error_message,
                ], 403);
            }
        } elseif ($type == 'text-with-location') {
            $validated = $request->validate([
                'degreesLatitude' => 'required|max:100',
                'degreesLongitude' => 'required|max:100',
            ]);
        } elseif ($type == 'text-with-list') {
            $validated = $request->validate([
                'header_title' => 'required|max:30',
                'message' => 'required|max:300',
                'footer_text' => 'required|max:30',
                'button_text' => 'required|max:30',
                'section.*' => 'required|max:1000',

            ]);

            $is_valid = count($request->section ?? []) > 20 ? false : true;
            $error_message = __('Maximum Section Limit Is 20');

            if ($is_valid == false) {
                return response()->json([
                    'message' => $error_message,
                ], 403);
            }



            foreach ($request->section as $key => $section) {

                if (count($section['value'] ?? []) == 0) {
                    $is_valid = false;
                    $error_message = __('Fill up the list option value');

                    break;
                } elseif ($section['title'] == null || !$section['title']) {
                    $is_valid = false;
                    $error_message = __('Fill up all the title field');

                    break;
                } elseif (strlen($section['title']) > 50) {
                    $is_valid = false;
                    $error_message = __('Maximum title limit is 50');

                    break;
                } else {
                    foreach ($section['value'] as $value_key => $value) {
                        if (empty($value['title'])) {
                            $is_valid = false;
                            $error_message = __('Option title is required');

                            break;
                        } elseif (strlen($value['title']) > 50) {
                            $is_valid = false;
                            $error_message = __('List value name maximum word limit is 50');

                            break;
                        } elseif (strlen($value['description']) > 50) {
                            $is_valid = false;
                            $error_message = __('List value description maximum word limit is 50');

                            break;
                        }
                    }
                }
            }

            if ($is_valid == false) {
                return response()->json([
                    'message' => $error_message,
                ], 403);
            }
        }


        $template = $this->saveTemplate($request->all(), $request->message, $type, Auth::id(), $id);

        return response()->json([
            'message' => __('Template Updated Successfully..!!'),
        ], 200);
    }

    public function destroy($id)
    {
        $template = Template::where('user_id', Auth::id())->findorFail($id);
        if ($template->type == 'text-with-media') {
            $file = '';

            if (isset($template->body['image'])) {
                $file = $template->body['image']['url'];
            } elseif (isset($template->body['attachment'])) {
                $file = $template->body['attachment']['url'];
            }



            if ($file != '') {
                $fileArr = explode('uploads', $file);
                if (isset($fileArr[1])) {
                    $file = 'uploads' . $fileArr[1];
                    if (Storage::exists($file)) {
                        Storage::delete($file);
                    }
                }
            }
        }
        $template->delete();

        return response()->json([
            'message'  => __('Template deleted successfully..!!'),
            'redirect' =>  route('user.template.index')
        ], 200);
    }

    public function viewtemplate(Request $request, $uuid)
    {
        // $device_id = $uuid;
        $device = Device::where('user_id', Auth::id())->where('uuid', $uuid)->first();
        try {
            $templates = Template::where('user_id', Auth::id())
                ->where('status', 1)
                ->get();
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Something Went Wrong');
        }

        $total_templates = count($templates);
        return view('user.template.view', compact('device', 'templates', 'total_templates'));
    }

    /**
     * Convert form data to RCS API JSON format
     */
    private function convertToRcsApiFormat($request, $templateType)
    {
        switch ($templateType) {
            case 'plain-text':
                return $this->formatPlainTextTemplate($request);

            case 'text-with-media':
                return $this->formatMediaTemplate($request);

            case 'rich-card':
                return $this->formatRichCardTemplate($request);

            case 'rich-card-carousel':
                return $this->formatCarouselTemplate($request);

            default:
                return $this->formatPlainTextTemplate($request);
        }
    }

    /**
     * Format plain text template for RCS API
     */
    private function formatPlainTextTemplate($request)
    {
        $content = [
            'content' => [
                'plainText' => $request->template_text
            ]
        ];

        // Add suggestions if any
        $suggestions = $this->formatSuggestions($request);
        // dd($suggestions);
        if (!empty($suggestions)) {
            $content['content']['suggestions'] = $suggestions;
        }

        return $content;
    }

    /**
     * Format media template for RCS API
     */
    private function formatMediaTemplate($request)
    {
        $content = [
            'content' => [
                'plainText' => $request->template_text
            ]
        ];

        // Upload media file to RCS API if provided
        if ($request->hasFile('media_file')) {
            $rcsApiService = new RcsApiService();

            // Try to get agent ID from selected device if available
            $agentId = null;
            if ($request->has('device_id')) {
                $device = Device::where('user_id', Auth::id())->where('id', $request->device_id)->first();
                $agentId = $device ? $device->token : null;
            }

            $uploadResult = $rcsApiService->uploadFile($request->file('media_file'), $agentId);

            if ($uploadResult['success']) {
                // Store the RCS file URL for future use
                $content['rcs_media_url'] = $uploadResult['file_url'];
                // Also store local backup
                $content['local_media_url'] = $this->saveFile($request, 'media_file');
            } else {
                // Fallback to local storage if RCS upload fails
                $content['local_media_url'] = $this->saveFile($request, 'media_file');
                $content['upload_error'] = $uploadResult['error'];
            }
        }

        return $content;
    }

    /**
     * Format rich card template for RCS API
     */
    private function formatRichCardTemplate($request)
    {
        $cardContent = [
            'cardTitle' => $request->card_title,
            'cardDescription' => $request->card_description
        ];

        // Add media if provided
        if ($request->hasFile('card_media_file')) {
            $rcsApiService = new RcsApiService();

            // Try to get agent ID from selected device if available
            $agentId = null;
            if ($request->has('device_id')) {
                $device = Device::where('user_id', Auth::id())->where('id', $request->device_id)->first();
                $agentId = $device ? $device->token : null;
            }

            $uploadResult = $rcsApiService->uploadFile($request->file('card_media_file'), $agentId);

            if ($uploadResult['success']) {
                // Use RCS uploaded file URL
                $mediaUrl = $uploadResult['file_url'];
                // Also store local backup
                $localMediaUrl = $this->saveFile($request, 'card_media_file');
            } else {
                // Fallback to local storage if RCS upload fails
                $mediaUrl = $this->saveFile($request, 'card_media_file');
                $localMediaUrl = $mediaUrl;
            }

            $cardContent['cardMedia'] = [
                'mediaHeight' => $request->card_height ?? 'MEDIUM',
                'contentInfo' => [
                    'fileUrl' => $mediaUrl
                ]
            ];

            // Store both URLs for reference
            $cardContent['_meta'] = [
                'rcs_media_url' => $uploadResult['success'] ? $uploadResult['file_url'] : null,
                'local_media_url' => $localMediaUrl,
                'upload_error' => $uploadResult['success'] ? null : $uploadResult['error'],
                'agent_id_used' => $agentId
            ];
        }

        // Add suggestions
        $suggestions = $this->formatCardSuggestions($request);
        if (!empty($suggestions)) {
            $cardContent['suggestions'] = $suggestions;
        }

        return [
            'content' => [
                'richCardDetails' => [
                    'standalone' => [
                        'cardOrientation' => $request->card_orientation ?? 'VERTICAL',
                        'content' => $cardContent
                    ]
                ]
            ]
        ];
    }

    /**
     * Format carousel template for RCS API
     */
    private function formatCarouselTemplate($request)
    {
        $rcsApiService = new RcsApiService();

        // Try to get agent ID from selected device if available
        $agentId = null;
        if ($request->has('device_id')) {
            $device = Device::where('user_id', Auth::id())->where('id', $request->device_id)->first();
            $agentId = $device ? $device->token : null;
        }

        $carouselCards = [];
        $uploadErrors = [];

        // Process up to 4 cards for carousel
        for ($i = 1; $i <= 4; $i++) {
            $cardTitle = $request->input("card{$i}_title");
            $cardDescription = $request->input("card{$i}_description");

            // Skip empty cards
            if (empty($cardTitle) && empty($cardDescription)) {
                continue;
            }

            $cardContent = [
                'cardTitle' => $cardTitle ?? "Card {$i}",
                'cardDescription' => $cardDescription ?? "Description for card {$i}"
            ];

            // Handle media file for this card
            if ($request->hasFile("card{$i}_media_file")) {
                $uploadResult = $rcsApiService->uploadFile($request->file("card{$i}_media_file"), $agentId);

                if ($uploadResult['success']) {
                    // Use RCS uploaded file URL
                    $mediaUrl = $uploadResult['file_url'];
                    // Also store local backup
                    $localMediaUrl = $this->saveFile($request, "card{$i}_media_file");
                } else {
                    // Fallback to local storage if RCS upload fails
                    $mediaUrl = $this->saveFile($request, "card{$i}_media_file");
                    $localMediaUrl = $mediaUrl;
                    $uploadErrors["card{$i}"] = $uploadResult['error'];
                }

                $cardContent['cardMedia'] = [
                    'mediaHeight' => $request->input('card_height', 'MEDIUM'),
                    'contentInfo' => [
                        'fileUrl' => $mediaUrl
                    ]
                ];

                // Store metadata for this card
                $cardContent['_meta'] = [
                    'rcs_media_url' => $uploadResult['success'] ? $uploadResult['file_url'] : null,
                    'local_media_url' => $localMediaUrl,
                    'upload_error' => $uploadResult['success'] ? null : $uploadResult['error'],
                    'agent_id_used' => $agentId
                ];
            }

            // Add suggestions for this card
            $suggestions = $this->formatCarouselCardSuggestions($request, $i);
            if (!empty($suggestions)) {
                $cardContent['suggestions'] = $suggestions;
            }

            $carouselCards[] = $cardContent;
        }

        // If no cards were created, create a default card
        if (empty($carouselCards)) {
            $carouselCards[] = [
                'cardTitle' => 'Default Card',
                'cardDescription' => 'Please configure your carousel cards'
            ];
        }

        return [
            'content' => [
                'richCardDetails' => [
                    'carousel' => [
                        'cardWidth' => $request->input('card_width', 'MEDIUM'),
                        'contents' => $carouselCards
                    ]
                ]
            ],
            '_carousel_meta' => [
                'total_cards' => count($carouselCards),
                'upload_errors' => $uploadErrors,
                'agent_id_used' => $agentId
            ]
        ];
    }

    /**
     * Format suggestions for plain text templates
     */
    private function formatSuggestions($request)
    {
        $suggestions = [];

        // Process suggestions from form
        for ($i = 1; $i <= 4; $i++) {
            $suggestionType = $request->input("card_suggestion{$i}_type_pt");

            if (empty($suggestionType)) {
                continue;
            }

            $suggestion = [];

            switch ($suggestionType) {
                case 'reply':
                    $suggestion['reply'] = [
                        'plainText' => $request->input("card_suggestion{$i}_text_pt", "Reply"),
                        'postBack' => [
                            'data' => "reply_suggestion_{$i}"
                        ]
                    ];
                    break;

                case 'openUrlAction':
                    $suggestion['action'] = [
                        'plainText' => $request->input("card_suggestion{$i}_text_pt", "Open URL"),
                        'postBack' => [
                            'data' => "url_suggestion_{$i}"
                        ],
                        'openUrl' => [
                            'url' => $request->input("card_suggestion{$i}_url_pt", "https://example.com")
                        ]
                    ];
                    break;

                case 'dialAction':
                    $suggestion['action'] = [
                        'plainText' => $request->input("card_suggestion{$i}_text_pt", "Call"),
                        'postBack' => [
                            'data' => "dial_suggestion_{$i}"
                        ],
                        'dialerAction' => [
                            'phoneNumber' => $request->input("card_suggestion{$i}_phone_pt", "+1234567890")
                        ]
                    ];
                    break;

                case 'shareLocation':
                    $latLong = $request->input("card_suggestion{$i}_latlong_pt", "0,0");
                    $coords = explode(',', $latLong);
                    $suggestion['action'] = [
                        'plainText' => $request->input("card_suggestion{$i}_text_pt", "View Location"),
                        'postBack' => [
                            'data' => "location_suggestion_{$i}"
                        ],
                        'showLocation' => [
                            'coordinAtes' => [
                                'latitude' => floatval(trim($coords[0] ?? 0)),
                                'longitude' => floatval(trim($coords[1] ?? 0))
                            ],
                            'label' => $request->input("card_suggestion{$i}_text_pt", "Location")
                        ]
                    ];
                    break;

                case 'createCalendarEventAction':
                    $suggestion['action'] = [
                        'plainText' => $request->input("card_suggestion{$i}_text_pt", "Add to Calendar"),
                        'postBack' => [
                            'data' => "calendar_suggestion_{$i}"
                        ],
                        'createCalendarEvent' => [
                            'startTime' => $request->input("card_suggestion{$i}_startTime_pt", "2024-01-01T10:00:00Z"),
                            'endTime' => $request->input("card_suggestion{$i}_endTime_pt", "2024-01-01T11:00:00Z"),
                            'title' => $request->input("card_suggestion{$i}_title_pt", "Event"),
                            'description' => $request->input("card_suggestion{$i}_description_pt", "Event description")
                        ]
                    ];
                    break;
                case 'openUrlAction':
                    $suggestion['action'] = [
                        'plainText' => $request->input("card_suggestion{$i}_text_pt", "Open URL"),
                        'postBack' => [
                            'data' => "url_suggestion_{$i}"
                        ],
                        'openUrl' => [
                            'url' => $request->input("card_suggestion{$i}_url_pt", "https://example.com")
                        ]
                    ];
                    break;
                case 'dialAction':
                    $suggestion['action'] = [
                        'plainText' => $request->input("card_suggestion{$i}_text_pt", "Call"),
                        'postBack' => [
                            'data' => "dial_suggestion_{$i}"
                        ],
                        'dialerAction' => [
                            'phoneNumber' => $request->input("card_suggestion{$i}_phone_pt", "+1234567890")
                        ]
                    ];
                    break;
                case 'shareLocation':
                    $latLong = $request->input("card_suggestion{$i}_latlong_pt", "0,0");
                    $coords = explode(',', $latLong);
                    $suggestion['action'] = [
                        'plainText' => $request->input("card_suggestion{$i}_text_pt", "View Location"),
                        'postBack' => [
                            'data' => "location_suggestion_{$i}"
                        ],
                        'showLocation' => [
                            'coordinAtes' => [
                                'latitude' => floatval(trim($coords[0] ?? 0)),
                                'longitude' => floatval(trim($coords[1] ?? 0))
                            ],
                            'label' => $request->input("card_suggestion{$i}_text_pt", "Location")
                        ]
                    ];
                    break;
            }

            if (!empty($suggestion)) {
                $suggestions[] = $suggestion;
            }
        }

        return $suggestions;
    }

    /**
     * Format suggestions for rich card templates
     */
    private function formatCardSuggestions($request)
    {
        $suggestions = [];

        // Process card suggestions from form
        for ($i = 1; $i <= 4; $i++) {
            $suggestionType = $request->input("card_suggestion{$i}_type");

            if (empty($suggestionType)) {
                continue;
            }

            $suggestion = [];

            switch ($suggestionType) {
                case 'reply':
                    $suggestion['reply'] = [
                        'plainText' => $request->input("card_suggestion{$i}_text", "Reply"),
                        'postBack' => [
                            'data' => "reply_suggestion_{$i}"
                        ]
                    ];
                    break;

                case 'openUrlAction':
                    $suggestion['action'] = [
                        'plainText' => $request->input("card_suggestion{$i}_text", "Open URL"),
                        'postBack' => [
                            'data' => "url_suggestion_{$i}"
                        ],
                        'openUrl' => [
                            'url' => $request->input("card_suggestion{$i}_url", "https://example.com")
                        ]
                    ];
                    break;

                case 'dialAction':
                    $suggestion['action'] = [
                        'plainText' => $request->input("card_suggestion{$i}_text", "Call"),
                        'postBack' => [
                            'data' => "dial_suggestion_{$i}"
                        ],
                        'dialerAction' => [
                            'phoneNumber' => $request->input("card_suggestion{$i}_phone", "+1234567890")
                        ]
                    ];
                    break;

                case 'shareLocation':
                    $latLong = $request->input("card_suggestion{$i}_latlong", "0,0");
                    $coords = explode(',', $latLong);
                    $suggestion['action'] = [
                        'plainText' => $request->input("card_suggestion{$i}_text", "View Location"),
                        'postBack' => [
                            'data' => "location_suggestion_{$i}"
                        ],
                        'showLocation' => [
                            'coordinAtes' => [
                                'latitude' => floatval(trim($coords[0] ?? 0)),
                                'longitude' => floatval(trim($coords[1] ?? 0))
                            ],
                            'label' => $request->input("card_suggestion{$i}_text", "Location")
                        ]
                    ];
                    break;

                case 'createCalendarEventAction':
                    $suggestion['action'] = [
                        'plainText' => $request->input("card_suggestion{$i}_text", "Add to Calendar"),
                        'postBack' => [
                            'data' => "calendar_suggestion_{$i}"
                        ],
                        'createCalendarEvent' => [
                            'startTime' => $request->input("card_suggestion{$i}_startTime", "2024-01-01T10:00:00Z"),
                            'endTime' => $request->input("card_suggestion{$i}_endTime", "2024-01-01T11:00:00Z"),
                            'title' => $request->input("card_suggestion{$i}_title", "Event"),
                            'description' => $request->input("card_suggestion{$i}_description", "Event description")
                        ]
                    ];
                    break;
            }

            if (!empty($suggestion)) {
                $suggestions[] = $suggestion;
            }
        }

        return $suggestions;
    }

    /**
     * Format suggestions for carousel card templates
     */
    private function formatCarouselCardSuggestions($request, $cardNumber)
    {
        $suggestions = [];

        // Process card suggestions from form for specific card
        for ($i = 1; $i <= 4; $i++) {
            $suggestionType = $request->input("card{$cardNumber}_suggestion{$i}_type");

            if (empty($suggestionType)) {
                continue;
            }

            $suggestion = [];

            switch ($suggestionType) {
                case 'reply':
                    $suggestion['reply'] = [
                        'plainText' => $request->input("card{$cardNumber}_suggestion{$i}_text", "Reply"),
                        'postBack' => [
                            'data' => "card{$cardNumber}_reply_suggestion_{$i}"
                        ]
                    ];
                    break;

                case 'openUrlAction':
                    $suggestion['action'] = [
                        'plainText' => $request->input("card{$cardNumber}_suggestion{$i}_text", "Open URL"),
                        'postBack' => [
                            'data' => "card{$cardNumber}_url_suggestion_{$i}"
                        ],
                        'openUrl' => [
                            'url' => $request->input("card{$cardNumber}_suggestion{$i}_url", "https://example.com")
                        ]
                    ];
                    break;

                case 'dialAction':
                    $suggestion['action'] = [
                        'plainText' => $request->input("card{$cardNumber}_suggestion{$i}_text", "Call"),
                        'postBack' => [
                            'data' => "card{$cardNumber}_dial_suggestion_{$i}"
                        ],
                        'dialerAction' => [
                            'phoneNumber' => $request->input("card{$cardNumber}_suggestion{$i}_phone", "+1234567890")
                        ]
                    ];
                    break;

                case 'shareLocation':
                    $latLong = $request->input("card{$cardNumber}_suggestion{$i}_latlong", "0,0");
                    $coords = explode(',', $latLong);
                    $suggestion['action'] = [
                        'plainText' => $request->input("card{$cardNumber}_suggestion{$i}_text", "View Location"),
                        'postBack' => [
                            'data' => "card{$cardNumber}_location_suggestion_{$i}"
                        ],
                        'showLocation' => [
                            'coordinAtes' => [
                                'latitude' => floatval(trim($coords[0] ?? 0)),
                                'longitude' => floatval(trim($coords[1] ?? 0))
                            ],
                            'label' => $request->input("card{$cardNumber}_suggestion{$i}_text", "Location")
                        ]
                    ];
                    break;

                case 'createCalendarEventAction':
                    $suggestion['action'] = [
                        'plainText' => $request->input("card{$cardNumber}_suggestion{$i}_text", "Add to Calendar"),
                        'postBack' => [
                            'data' => "card{$cardNumber}_calendar_suggestion_{$i}"
                        ],
                        'createCalendarEvent' => [
                            'startTime' => $request->input("card{$cardNumber}_suggestion{$i}_startTime", "2024-01-01T10:00:00Z"),
                            'endTime' => $request->input("card{$cardNumber}_suggestion{$i}_endTime", "2024-01-01T11:00:00Z"),
                            'title' => $request->input("card{$cardNumber}_suggestion{$i}_title", "Event"),
                            'description' => $request->input("card{$cardNumber}_suggestion{$i}_description", "Event description")
                        ]
                    ];
                    break;
            }

            if (!empty($suggestion)) {
                $suggestions[] = $suggestion;
            }
        }

        return $suggestions;
    }
}
