<?php

namespace App\Http\Controllers\User;

use App\Jobs\BulkInsertTaskJob;
use App\Http\Controllers\Controller;
use App\Models\Contact;
use App\Models\TutorialVideo;
use Illuminate\Http\Request;
use App\Models\Smstransaction;
use App\Models\Device;
use App\Models\Group;
use App\Models\Task;
use App\Models\Template;
use App\Rules\Phone;
use App\Traits\Whatsapp;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class CustomTextController extends Controller
{

    use Whatsapp;

    public function index()
    {
        $devices = Device::where('user_id', Auth::id())->where('status', 1)->latest()->get();

        $templates = Template::where('user_id', Auth::id())
            ->where('status', 1)
            ->get();

        // $device_data = [];
        // $i = 0;
        // foreach ($devices  as $device) {

        //     $templates = getFacebookTemplates($device->waid, $device->token) ?? [];
        //     $id = $device->id;
        //     $device_data[$id]['uuid'] = $device->uuid;
        //     $device_data[$id]['templates'] = $templates;
        //     $device_data[$id]['id'] = $device->id;
        //     $device_data[$id]['name'] = $device->name;
        //     $device_data[$id]['phone'] = $device->phone;
        //     $i++;
        // }

        $group_list = DB::table('groups')
            ->select('groups.id', 'groups.user_id', 'groups.name', DB::raw('count(groupcontacts.group_id) as total_member'))
            ->join('groupcontacts', 'groupcontacts.group_id', '=', 'groups.id')
            ->where('groups.user_id', Auth::id())
            ->groupBy('groups.id', 'groups.user_id', 'groups.name')
            ->get();

        $phoneCodes = file_exists('uploads/phonecode.json') ? json_decode(file_get_contents('uploads/phonecode.json')) : [];

        $videoTutorial = TutorialVideo::where('key', 'send_whatsapp')->first();

        return view('user.singlesend.create', compact('templates', 'phoneCodes', 'devices', 'group_list', 'videoTutorial'));
    }

    public function sentCustomText(Request $request)
    {
        if (api_plan() && api_plan()->title == 'Api') {
            return redirect()->back()->with('error', 'Send whatsapp features is not available with your subscription');
        }

        $validated = $request->validate([
            'campaign_device' => 'required',
            'template_select' => 'required'
        ]);

        $device_id = $request->campaign_device;
        $campaign_numbers = $request->campaign_numbers;
        $groups = $request->campaign_groups;
        $text = $request->campaign_text;
        $campaign_name = $request->campaign_name;
        $templateId = $request->template_select;
        $language = $request->language ?? 'en';
        $task_type = $request->task_type ?? 2;
        $parameters = is_array($request->variable) ? implode("||", $request->variable) : null;
        // $flow_id = $request->flow ?? null;
        // $scheduled_on = $request->schedule_on ?? now();

        if (is_array($campaign_numbers)) {
            $campaign_numbers = implode(PHP_EOL, $campaign_numbers);
        }

        $campaign_numbers_array = explode(PHP_EOL, $campaign_numbers);

        $campaign_numbers_array = array_filter($campaign_numbers_array, 'trim');

        $campaign_numbers_array = array_unique($campaign_numbers_array);

        $campaign_numbers_array = array_filter($campaign_numbers_array, function ($item) {
            return strlen($item) >= 10;
        });

        if ($groups !== null && $groups !== "") {
            $groupModel = new Group();
            $group_numbers = $groupModel->get_group_numbers($groups);
            $campaign_numbers_array = array_merge($campaign_numbers_array, $group_numbers);
            $campaign_numbers_array = array_unique($campaign_numbers_array, SORT_REGULAR);
        }

        if ($request->has_media == 1 && $request->hasFile('media_file')) {
            $media_file_data = $request->file('media_file');
            $filename = uniqid() . '.' . $media_file_data->getClientOriginalExtension();
            $media_file_data->move(public_path('whatsappmedia'), $filename);
            $media_url = asset("whatsappmedia/{$filename}");
        } else {
            $media_url = null;
        }

        $created_by = Auth::id();
        $ip = request()->ip();
        $launch_time = now();
        if (count($campaign_numbers_array) > 500) {
            $scheduled_on = $request->schedule_on ?? now()->addMinutes(5);
        } else {
            $scheduled_on = $request->schedule_on ?? now();
        }
        $taskdata = [
            'device_id' => $device_id,
            'created_by' => $created_by,
            'launched_on' => $launch_time,
            'scheduled_on' => $scheduled_on,
            'task_url' => $media_url,
            'campaign_name' => $campaign_name,
            'templateId' => $templateId,
            'language' => 'en',
            'task_type' => 1,
            'parameters' => $parameters,
            // 'flow_id' => $flow_id,
            'text' => $text,
            'ip' => $ip,
        ];
        // Split campaign numbers into smaller chunks
        $batchSize = 1000;
        $delay = 0; // Start with no delay
        $campaignChunks = array_chunk($campaign_numbers_array, $batchSize);
        try {
            foreach ($campaignChunks as $chunk) {
                BulkInsertTaskJob::dispatch($taskdata, $chunk)
                    ->onQueue('high')
                    ->delay(now()->addMinutes($delay)); // Add delay

                $delay = $delay + 2; // Increment delay for next job
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error dispatching the job: ' . $e->getMessage());
        }
        return redirect(url('user/sent-text-message'))->with('success', 'Send Whatsapp Successfully');
    }
}
