{"info": {"_postman_id": "7cd086eb-7f0e-4548-abdc-e01c9891dac6", "name": "RCS PROD Updated", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "31023354"}, "item": [{"name": "Plain Text", "item": [{"name": "Plain Text with SMS", "item": [{"name": "Plain Text with SMS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"4a9c72a9-1d13-4030-994f-5cc857603445\",\n    \"agentID\": \"Your agentID\",\n    \"campaignID\": \"today check\",\n    \"contacts\": [\n        // \"+918779619155\",\n        // \"+919321921653\",\n        // \"+919324900790\"\n        \"9321921653\"\n        // \"+919867614567\",\n        // \"+918451059612\",\n        // \"7977624341\",\n        // \"8380024605\",\n        // \"+91232323\"\n    ],    \"data\": {\n        \"content\": {\n            \"plainText\": \"Looking for complete protection new new \\\"under one plan at\\\" 4s\"\n        }\n    },\n    \"data_sms\": {\n    \"sender_id\": \"Add Sender ID\",\n    \"domain_id\": \"Add Domain ID\",\n    \"sms_type\": \"T\",\n    \"sms_content_type\": \"Static\",\n    \"dlt_entity_id\": \"12044854436650\",\n    \"body\": \"SMS Body\",\n    \"dlt_template_id\": \"120717281037\"\n    }\n}"}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}]}, {"name": "Plain Text without SMS", "item": [{"name": "Plain Text", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Agent key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"4a9c72a9-1d13-4030-994f-5cc857603445\",\n    \"agentID\": \"Your agent ID\",\n    \"campaignID\": \"today check\",\n    \"contacts\": [\n        \"+918779619155\"\n        // \"+919321921653\",\n        // \"+919324900790\"\n        //\"9321921653\",\n        // \"+919867614567\",\n        // \"+918451059612\",\n        // \"7977624341\",\n        // \"8380024605\",\n        // \"+91232323\"\n    ],\n    \"data\": {\n        \"content\": {\n            \"plainText\": \"Your order #98565487654 has been delivered. Thank you for shopping with <PERSON>nitch!\"\n        }\n    }\n}"}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Plain Text with CTA", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "WQMMNdkgBp2ChEOGoxzajrZuGINXPy04"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"4a9c72a9-1d13-4030-994f-5cc857603445\",\n    \"agentID\": \"Add your agent ID\",\n    \"campaignID\": \"today check\",\n    \"contacts\": [\n        \"+919853186\"\n    ],\n    \"data\": {\n        \"content\": {\n            \"plainText\": \"Hi, Welcome to Alot Solutions.🥳🎉\",\n            \"suggestions\": [\n                {\n                    \"action\": {\n                        \"plainText\": \"Pay Now\",\n                        \"postBack\": {\n                            \"data\": \"call_back_data_for_button_1_goes_here{{$50}}\"\n                        },\n                        \"openUrl\": {\n                            \"url\": \"https://www.google.in/\"\n                        }\n                    }\n                }\n            ]\n        }\n    }\n}"}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Plain Text with <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "WQMMNdkgBp2ChEOGoxzajrZuGINXPy04"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"4a9c72a9-1d13-4030-994f-5cc857603445\",\n    \"agentID\": \"Add your agent ID\",\n    \"campaignID\": \"today check\",\n    \"contacts\": [\n        \"+919853186\"\n    ],\n    \"data\": {\n        \"content\": {\n            \"plainText\": \"Hi, Welcome to Alot Solutions.🥳🎉\",\n            \"suggestions\": [\n                {\n                    \"action\": {\n                        \"plainText\": \"Pay Now\",\n                        \"postBack\": {\n                            \"data\": \"call_back_data_for_button_1_goes_here{{$50}}\"\n                        },\n                        \"openUrl\": {\n                            \"url\": \"https://www.google.in/\"\n                        }\n                    }\n                }\n            ]\n        }\n    }\n}"}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Plain Text with Suggestion", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "WQMMNdkgBp2ChEOGoxzajrZuGINXPy04"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"4a9c72a9-1d13-4030-994f-5cc857603445\",\n    \"agentID\": \"Add your agent ID\",\n    \"campaignID\": \"today check\",\n    \"contacts\": [\n        \"+91983186\"\n    ],\n    \"data\": {\n        \"content\": {\n            \"plainText\": \"Your order #98565487654 has been delivered. Thank you for shopping with <PERSON>ni<PERSON>!\",\n            \"suggestions\": [\n                {\n                    \"reply\": {\n                        \"plainText\": \"Yes, Absolutely\",\n                        \"postBack\": {\n                            \"data\": \"visit_now_election24{{$50}}\"\n                        }\n                    }\n                }\n            ]\n        }\n    }\n}"}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}]}]}, {"name": "Standalone", "item": [{"name": "Standalone with SMS", "item": [{"name": "Standalone Send Msg with SMS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"ce772a8f-c66b-49b4-95a9-491d2197e826\",\n    \"agentID\": \"Your AgentID\",\n    \"contacts\": [\n        \"+918779619155\"\n    ],\n    \"data\": {\n       \"content\": {\n            \"richCardDetails\": {\n                \"standalone\": {\n                    \"cardOrientation\": \"VERTICAL\",\n                    \"content\": {\n                        \"cardTitle\": \"card title\",\n                        \"cardDescription\": \"description\",\n                        \"cardMedia\": {\n                            \"mediaHeight\": \"MEDIUM\",\n                            \"contentInfo\": {\n                                \"fileUrl\": \"https://jfxv.akamaized.net/PublicStorage/Bot/66e025b800adf197e64fffbc/1727684177481.png\"\n                            }\n                        },\n                        \"suggestions\": [\n                            {\n                                \"action\": {\n                                    \"plainText\": \"Visit Now\",\n                                    \"postBack\": {\n                                        \"data\": \"visit_now_election24{{$50}}\"\n                                    },\n                                    \"openUrl\": {\n                                        \"url\": \"https://elections24.eci.gov.in/\"\n                                    }\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    },\n    \"data_sms\": {\n    \"sender_id\": \"Add your Sender ID\",\n    \"domain_id\": \"Add your Domain ID\",\n    \"sms_type\": \"T\",\n    \"sms_content_type\": \"Static\",\n    \"dlt_entity_id\": \"1201854436650\",\n    \"body\": \"SMS Body\",\n    \"dlt_template_id\": \"120711037\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Standalone with Suggested Reply and SMS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"ce772a8f-c66b-49b4-95a9-491d2197e819\",\n    \"agentID\": \"Your AgentID\",\n    \"contacts\": [\n        \"+918779619155\"\n    ],\n    \"data\": {\n        \"content\": {\n            \"richCardDetails\": {\n                \"standalone\": {\n                    \"cardOrientation\": \"VERTICAL\",\n                    \"content\": {\n                        \"cardTitle\": \"card title\",\n                        \"cardDescription\": \"description\",\n                        \"cardMedia\": {\n                            \"mediaHeight\": \"MEDIUM\",\n                            \"contentInfo\": {\n                                \"fileUrl\": \"https://jfxv.akamaized.net/PublicStorage/Bot/66e025b800adf197e64fffbc/1727684177481.png\"\n                            }\n                        },\n                        \"suggestions\": [\n                            {\n                                \"reply\": {\n                                    \"plainText\": \"Yes, Absolutely\",\n                                    \"postBack\": {\n                                        \"data\": \"suggestion_1\"\n                                    }\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    },\n    \"data_sms\": {\n    \"sender_id\": \"Add Sender ID here\",\n    \"domain_id\": \"Add Domain ID here\",\n    \"sms_type\": \"T\",\n    \"sms_content_type\": \"Static\",\n    \"dlt_entity_id\": \"1201854436650\",\n    \"body\": \"SMS Body\",\n    \"dlt_template_id\": \"120717191037\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Standalone with Suggested Calender event and SMS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"ce772a8f-c66b-49b4-95a9-491d2197e819\",\n    \"agentID\": \"Your AgentID\",\n    \"contacts\": [\n        \"+918779619155\"\n    ],\n    \"data\": {\n        \"content\": {\n            \"richCardDetails\": {\n                \"standalone\": {\n                    \"cardOrientation\": \"VERTICAL\",\n                    \"content\": {\n                        \"cardTitle\": \"card title\",\n                        \"cardDescription\": \"description\",\n                        \"cardMedia\": {\n                            \"mediaHeight\": \"MEDIUM\",\n                            \"contentInfo\": {\n                                \"fileUrl\": \"https://jfxv.akamaized.net/PublicStorage/Bot/66e025b800adf197e64fffbc/1727684177481.png\"\n                            }\n                        },\n                        \"suggestions\": [\n                            {\n                                \"action\": {\n                                    \"plainText\": \"Mark Your Calendar\",\n                                    \"postBack\": {\n                                        \"data\": \"SA1L1C1{{$50}}\"\n                                    },\n                                    \"createCalendarEvent\": {\n                                        \"startTime\": \"2023-06-26T15:01:23Z\",\n                                        \"endTime\": \"2023-06-26T18:01:23Z\",\n                                        \"title\": \"RCS Seminar\",\n                                        \"description\": \"Session 1 of 4\"\n                                    }\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    },\n    \"data_sms\": {\n    \"sender_id\": \"Add Sender ID\",\n    \"domain_id\": \"Add Domain ID\",\n    \"sms_type\": \"T\",\n    \"sms_content_type\": \"Static\",\n    \"dlt_entity_id\": \"12036650\",\n    \"body\": \"SMS Body\",\n    \"dlt_template_id\": \"12071017191037\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Standalone Suggested Share Location and SMS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"ce772a8f-c66b-49b4-95a9-491d2197e819\",\n    \"agentID\": \"Your AgentID\",\n    \"contacts\": [\n        \"+918779619155\"\n    ],\n    \"data\": {\n        \"content\": {\n            \"richCardDetails\": {\n                \"standalone\": {\n                    \"cardOrientation\": \"VERTICAL\",\n                    \"content\": {\n                        \"cardTitle\": \"card title\",\n                        \"cardDescription\": \"description\",\n                        \"cardMedia\": {\n                            \"mediaHeight\": \"MEDIUM\",\n                            \"contentInfo\": {\n                                \"fileUrl\": \"https://jfxv.akamaized.net/PublicStorage/Bot/66e025b800adf197e64fffbc/1727684177481.png\"\n                            }\n                        },\n                        \"suggestions\": [\n                            {\n                                \"action\": {\n                                    \"plainText\": \"Visit Now\",\n                                    \"postBack\": {\n                                        \"data\": \"SA1L1C1{{$50}}\"\n                                    },\n                                    \"showLocation\": {\n                                        \"coordinAtes\": {\n                                            \"latitude\": 21.5937,\n                                            \"longitude\": 78.9629\n                                        },\n                                        \"label\": \"Label - Show Location\"\n                                    }\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    },\n    \"data_sms\": {\n    \"sender_id\": \"Add Sender ID\",\n    \"domain_id\": \"jiocxrcsAdd Domain ID\",\n    \"sms_type\": \"T\",\n    \"sms_content_type\": \"Static\",\n    \"dlt_entity_id\": \"120136650\",\n    \"body\": \"SMS Body\",\n    \"dlt_template_id\": \"120791037\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Standalone with Suggested Dialer Action and SMS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"ce772a8f-c66b-49b4-95a9-491d2197e819\",\n    \"agentID\": \"Your AgentID\",\n    \"contacts\": [\n        \"+918779619155\"\n    ],\n    \"data\": {\n        \"content\": {\n            \"richCardDetails\": {\n                \"standalone\": {\n                    \"cardOrientation\": \"VERTICAL\",\n                    \"content\": {\n                        \"cardTitle\": \"card title\",\n                        \"cardDescription\": \"description\",\n                        \"cardMedia\": {\n                            \"mediaHeight\": \"MEDIUM\",\n                            \"contentInfo\": {\n                                \"fileUrl\": \"https://jfxv.akamaized.net/PublicStorage/Bot/65e05f504d40859b33cf5854/172837166462.jpeg\"\n                            }\n                        },\n                        \"suggestions\": [\n                            {\n                                \"action\": {\n                                    \"plainText\": \"Dial Now\",\n                                    \"postBack\": {\n                                        \"data\": \"SA1L1C1{{$50}}\"\n                                    },\n                                    \"dialerAction\": {\n                                        \"phoneNumber\": \"+918779619155\"\n                                    }\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    },\n    \"data_sms\": {\n    \"sender_id\": \"Add Sender ID\",\n    \"domain_id\": \"Add Domain ID\",\n    \"sms_type\": \"T\",\n    \"sms_content_type\": \"Static\",\n    \"dlt_entity_id\": \"12044854436650\",\n    \"body\": \"SMS Body\",\n    \"dlt_template_id\": \"12071037\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Standalone with Suggested Open URL and SMS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"ce772a8f-c66b-49b4-95a9-491d2197e819\",\n    \"agentID\": \"Your AgentID\",\n    \"contacts\": [\n        \"+918779619155\"\n    ],\n    \"data\": {\n       \"content\": {\n            \"richCardDetails\": {\n                \"standalone\": {\n                    \"cardOrientation\": \"VERTICAL\",\n                    \"content\": {\n                        \"cardTitle\": \"card title\",\n                        \"cardDescription\": \"description\",\n                        \"cardMedia\": {\n                            \"mediaHeight\": \"MEDIUM\",\n                            \"contentInfo\": {\n                                \"fileUrl\": \"https://jfxv.akamaized.net/PublicStorage/Bot/66e025b800adf197e64fffbc/1727684177481.png\"\n                            }\n                        },\n                        \"suggestions\": [\n                            {\n                                \"action\": {\n                                    \"plainText\": \"Visit Now\",\n                                    \"postBack\": {\n                                        \"data\": \"visit_now_election24{{$50}}\"\n                                    },\n                                    \"openUrl\": {\n                                        \"url\": \"https: //elections24.eci.gov.in/\"\n                                    }\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    },\n    \"data_sms\": {\n    \"sender_id\": \"Add Sender ID\",\n    \"domain_id\": \"Add Domain ID\",\n    \"sms_type\": \"T\",\n    \"sms_content_type\": \"Static\",\n    \"dlt_entity_id\": \"12014436650\",\n    \"body\": \"SMS Body\",\n    \"dlt_template_id\": \"120717191037\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}], "description": "Standalone with Suggested reply, Calender event, Share Location, Dialer Action and Open URL and SMS"}, {"name": "Standalone Without SMS", "item": [{"name": "Standalone Send Msg", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Agent Key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"ce772a8f-c66b-49b4-95a9-491d2197e826\",\n    \"agentID\": \"Your agent ID\",\n    \"contacts\": [\n        \"+919833353186\"\n    ],\n    \"data\": {\n       \"content\": {\n            \"richCardDetails\": {\n                \"standalone\": {\n                    \"cardOrientation\": \"VERTICAL\",\n                    \"content\": {\n                        \"cardTitle\": \"Order Confirmation\",\n                        \"cardDescription\": \"Your Snitch order is confirmed. Your order ID is #876593736545 and the total amount is 897 INR. You can expect your delivery on 16th November 2024.Thank you for shopping with Snitch!\",\n                        \"cardMedia\": {\n                            \"mediaHeight\": \"MEDIUM\",\n                            \"contentInfo\": {\n                                \"fileUrl\": \"https://jfxv.akamaized.net/PublicStorage/Bot/672084ae586ecfa6d41327fd/1731418947300.png\"\n                            }\n                        },\n                        \"suggestions\": [\n                            {\n                                \"action\": {\n                                    \"plainText\": \"Visit Now\",\n                                    \"postBack\": {\n                                        \"data\": \"visit_now_election24{{$50}}\"\n                                    },\n                                    \"openUrl\": {\n                                        \"url\": \"https://elections24.eci.gov.in/\"\n                                    }\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Standalone with Suggested Reply", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"ce772a8f-c66b-49b4-95a9-491d2197e819\",\n    \"agentID\": \"Your AgentID\",\n    \"contacts\": [\n        \"+918779619155\"\n    ],\n    \"data\": {\n        \"content\": {\n            \"richCardDetails\": {\n                \"standalone\": {\n                    \"cardOrientation\": \"VERTICAL\",\n                    \"content\": {\n                        \"cardTitle\": \"card title\",\n                        \"cardDescription\": \"description\",\n                        \"cardMedia\": {\n                            \"mediaHeight\": \"MEDIUM\",\n                            \"contentInfo\": {\n                                \"fileUrl\": \"https://jfxv.akamaized.net/PublicStorage/Bot/66e025b800adf197e64fffbc/1727684177481.png\"\n                            }\n                        },\n                        \"suggestions\": [\n                            {\n                                \"reply\": {\n                                    \"plainText\": \"Yes, Absolutely\",\n                                    \"postBack\": {\n                                        \"data\": \"visit_now_election24{{$50}}\"\n                                    }\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Standalone with Suggested Calender event", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"ce772a8f-c66b-49b4-95a9-491d2197e819\",\n    \"agentID\": \"Your AgentID\",\n    \"contacts\": [\n        \"+918779619155\"\n    ],\n    \"data\": {\n        \"content\": {\n            \"richCardDetails\": {\n                \"standalone\": {\n                    \"cardOrientation\": \"VERTICAL\",\n                    \"content\": {\n                        \"cardTitle\": \"card title\",\n                        \"cardDescription\": \"description\",\n                        \"cardMedia\": {\n                            \"mediaHeight\": \"MEDIUM\",\n                            \"contentInfo\": {\n                                \"fileUrl\": \"https://jfxv.akamaized.net/PublicStorage/Bot/66e025b800adf197e64fffbc/1727684177481.png\"\n                            }\n                        },\n                        \"suggestions\": [\n                            {\n                                \"action\": {\n                                    \"plainText\": \"Mark Your Calendar\",\n                                    \"postBack\": {\n                                        \"data\": \"SA1L1C1{{$50}}\"\n                                    },\n                                    \"createCalendarEvent\": {\n                                        \"startTime\": \"2023-06-26T15:01:23Z\",\n                                        \"endTime\": \"2023-06-26T18:01:23Z\",\n                                        \"title\": \"RCS Seminar\",\n                                        \"description\": \"Session 1 of 4\"\n                                    }\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Standalone Suggested Share Location", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"ce772a8f-c66b-49b4-95a9-491d2197e819\",\n    \"agentID\": \"Your AgentID\",\n    \"contacts\": [\n        \"+918779619155\"\n    ],\n    \"data\": {\n        \"content\": {\n            \"richCardDetails\": {\n                \"standalone\": {\n                    \"cardOrientation\": \"VERTICAL\",\n                    \"content\": {\n                        \"cardTitle\": \"card title\",\n                        \"cardDescription\": \"description\",\n                        \"cardMedia\": {\n                            \"mediaHeight\": \"MEDIUM\",\n                            \"contentInfo\": {\n                                \"fileUrl\": \"https://jfxv.akamaized.net/PublicStorage/Bot/66e025b800adf197e64fffbc/1727684177481.png\"\n                            }\n                        },\n                        \"suggestions\": [\n                            {\n                                \"action\": {\n                                    \"plainText\": \"Visit Now\",\n                                    \"postBack\": {\n                                        \"data\": \"SA1L1C1{{$50}}\"\n                                    },\n                                    \"showLocation\": {\n                                        \"coordinAtes\": {\n                                            \"latitude\": 21.5937,\n                                            \"longitude\": 78.9629\n                                        },\n                                        \"label\": \"Label - Show Location\"\n                                    }\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://rcsportalapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Standalone with Suggested Dialer Action", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"ce772a8f-c66b-49b4-95a9-491d2197e819\",\n    \"agentID\": \"Your AgentID\",\n    \"contacts\": [\n        \"+918779619155\"\n    ],\n    \"data\": {\n        \"content\": {\n            \"richCardDetails\": {\n                \"standalone\": {\n                    \"cardOrientation\": \"VERTICAL\",\n                    \"content\": {\n                        \"cardTitle\": \"card title\",\n                        \"cardDescription\": \"description\",\n                        \"cardMedia\": {\n                            \"mediaHeight\": \"MEDIUM\",\n                            \"contentInfo\": {\n                                \"fileUrl\": \"https://jfxv.akamaized.net/PublicStorage/Bot/65e05f504d40859b33cf5854/172837166462.jpeg\"\n                            }\n                        },\n                        \"suggestions\": [\n                            {\n                                \"action\": {\n                                    \"plainText\": \"Dial Now\",\n                                    \"postBack\": {\n                                        \"data\": \"SA1L1C1{{$50}}\"\n                                    },\n                                    \"dialerAction\": {\n                                        \"phoneNumber\": \"+918779619155\"\n                                    }\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Standalone with Suggested Open URL", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"ce772a8f-c66b-49b4-95a9-491d2197e819\",\n    \"agentID\": \"Your AgentID\",\n    \"contacts\": [\n        \"+918779619155\"\n    ],\n    \"data\": {\n       \"content\": {\n            \"richCardDetails\": {\n                \"standalone\": {\n                    \"cardOrientation\": \"VERTICAL\",\n                    \"content\": {\n                        \"cardTitle\": \"card title\",\n                        \"cardDescription\": \"description\",\n                        \"cardMedia\": {\n                            \"mediaHeight\": \"MEDIUM\",\n                            \"contentInfo\": {\n                                \"fileUrl\": \"https://jfxv.akamaized.net/PublicStorage/Bot/66e025b800adf197e64fffbc/1727684177481.png\"\n                            }\n                        },\n                        \"suggestions\": [\n                            {\n                                \"action\": {\n                                    \"plainText\": \"Visit Now\",\n                                    \"postBack\": {\n                                        \"data\": \"visit_now_election24{{$50}}\"\n                                    },\n                                    \"openUrl\": {\n                                        \"url\": \"https: //elections24.eci.gov.in/\"\n                                    }\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Standalone with Combination", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your Key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"ce772a8f-c66b-49b4-95a9-491d2197e819\",\n    \"agentID\": \"Your AgentID\",\n    \"contacts\": [\n        \"+918779619155\"\n    ],\n    \"data\": {\n        \"content\": {\n            \"richCardDetails\": {\n                \"standalone\": {\n                    \"cardOrientation\": \"VERTICAL\",\n                    \"content\": {\n                        \"cardTitle\": \"card title\",\n                        \"cardDescription\": \"description\",\n                        \"cardMedia\": {\n                            \"mediaHeight\": \"MEDIUM\",\n                            \"contentInfo\": {\n                                \"fileUrl\": \"https://jfxv.akamaized.net/PublicStorage/Bot/66e025b800adf197e64fffbc/1727684177481.png\"\n                            }\n                        },\n                        \"suggestions\": [\n                            {\n                                \"action\": {\n                                    \"plainText\": \"Visit Now\",\n                                    \"postBack\": {\n                                        \"data\": \"visit_now_election24{{$50}}\"\n                                    },\n                                    \"openUrl\": {\n                                        \"url\": \"https: //elections24.eci.gov.in/\"\n                                    }\n                                }\n                            },\n                            {\n                                \"action\": {\n                                    \"plainText\": \"Dial Now\",\n                                    \"postBack\": {\n                                        \"data\": \"SA1L1C1{{$50}}\"\n                                    },\n                                    \"dialerAction\": {\n                                        \"phoneNumber\": \"+918779619155\"\n                                    }\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}]}], "description": "Standalone with Suggested reply, Calender event, Share Location, Dialer Action and Open URL"}, {"name": "Carousel", "item": [{"name": "Carousel with SMS", "item": [{"name": "Carousel Send Msg with SMS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "Your key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"4a9c72a9-1d13-4030-994f-5cc857603445\",\n    \"agentID\": \"your  agent<PERSON>\",\n    \"campaignID\": \"today check\",\n    \"contacts\": [\n        \"+918779619155\"\n        // \"+919321921653\",\n        // \"+919324900790\"\n        // \"+919158710200\",\n        // \"+919867614567\",\n        // \"+918451059612\",\n        // \"7977624341\",\n        // \"+91232323\",\n        // \"+919421464605\"\n    ],\n    \"data\": {\n        \"Content\": {\n            \"richCardDetails\": {\n                \"carousel\": {\n                    \"cardWidth\": \"MEDIUM_WIDTH\",\n                    \"contents\": [\n                        {\n                            \"cardTitle\": \"RCS Infobip Testing\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/nf/n Your Cylinder will be delivered soon.  \",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Pay Now\",\n                                        \"postBack\": {\n                                            \"data\": \"call_back_data_for_button_1_goes_here {{$50}} \"\n                                        },\n                                        \"openUrl\": {\n                                            \"url\": \"https://www.ioclmd.in/\"\n                                        }\n                                    }\n                                }\n                            ]\n                        },\n                        {\n                            \"cardTitle\": \"RCS Apigee testing 2\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/n/n Your Cylinder will be delivered soon.\",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Pay Now\",\n                                        \"postBack\": {\n                                            \"data\": \"call_back_data_for_button_1_goes_here {{$50}} \"\n                                        },\n                                        \"openUrl\": {\n                                            \"url\": \"https://www.ioclmd.in/\"\n                                        }\n                                    }\n                                }\n                            ]\n                        },\n                        {\n                            \"cardTitle\": \"RCS Apigee testing 3\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/n/n Your Cylinder will be delivered soon.  \",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Pay Now\",\n                                        \"postBack\": {\n                                            \"data\": \"call_back_data_for_button_1_goes_here {{$50}} \"\n                                        },\n                                        \"openUrl\": {\n                                            \"url\": \"https://www.ioclmd.in/\"\n                                        }\n                                    }\n                                }\n                            ]\n                        },\n                        {\n                            \"cardTitle\": \"RCS Apigee testing 4\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/n/n Your Cylinder will be delivered soon.  \",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Pay Now\",\n                                        \"postBack\": {\n                                            \"data\": \"call_back_data_for_button_1_goes_here {{$50}} \"\n                                        },\n                                        \"openUrl\": {\n                                            \"url\": \"https://www.ioclmd.in/\"\n                                        }\n                                    }\n                                }\n                            ]\n                        }\n                    ]\n                }\n            }\n        }\n    },\n    \"data_sms\": {\n    \"sender_id\": \"Add Sender ID here\",\n    \"domain_id\": \"Add Domain ID here\",\n    \"sms_type\": \"T\",\n    \"sms_content_type\": \"Static\",\n    \"dlt_entity_id\": \"120154436650\",\n    \"body\": \"SMS Body\",\n    \"dlt_template_id\": \"120717191037\"\n    }\n}"}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Carousel with Suggested reply, Calender event, Share Location, Dialer Action and Open URL Copy  with SMS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "your key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"4a9c72a9-1d13-4030-994f-5cc857603445\",\n    \"agentID\": \"your agentID\",\n    \"campaignID\": \"today check\",\n    \"contacts\": [\n        \"+918779619155\"\n        // \"+919321921653\",\n        // \"+919324900790\"\n        // \"+919158710200\",\n        // \"+919867614567\",\n        // \"+918451059612\",\n        // \"7977624341\",\n        // \"+91232323\",\n        // \"+919421464605\"\n    ],\n    \"data\": {\n        \"Content\": {\n            \"richCardDetails\": {\n                \"carousel\": {\n                    \"cardWidth\": \"MEDIUM_WIDTH\",\n                    \"contents\": [\n                        {\n                            \"cardTitle\": \"RCS Infobip Testing\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/nf/n Your Cylinder will be delivered soon.  \",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Mark Your Calendar\",\n                                        \"postBack\": {\n                                            \"data\": \"SA1L1C1{{$50}}\"\n                                        },\n                                        \"createCalendarEvent\": {\n                                            \"startTime\": \"2023-06-26T15:01:23Z\",\n                                            \"endTime\": \"2023-06-26T18:01:23Z\",\n                                            \"title\": \"RCS Seminar\",\n                                            \"description\": \"Session 1 of 4\"\n                                        }\n                                    }\n                                }\n                            ]\n                        },\n                        {\n                            \"cardTitle\": \"RCS Apigee testing 2\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/n/n Your Cylinder will be delivered soon.\",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Visit Now\",\n                                        \"postBack\": {\n                                            \"data\": \"SA1L1C1{{$50}}\"\n                                        },\n                                        \"showLocation\": {\n                                            \"coordinAtes\": {\n                                                \"latitude\": 21.5937,\n                                                \"longitude\": 78.9629\n                                            },\n                                            \"label\": \"Label - Show Location\"\n                                        }\n                                    }\n                                }\n                            ]\n                        },\n                        {\n                            \"cardTitle\": \"RCS Apigee testing 3\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/n/n Your Cylinder will be delivered soon.  \",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Dial Now\",\n                                        \"postBack\": {\n                                            \"data\": \"SA1L1C1{{$50}}\"\n                                        },\n                                        \"dialerAction\": {\n                                            \"phoneNumber\": \"+918779619155\"\n                                        }\n                                    }\n                                }\n                            ]\n                        },\n                        {\n                            \"cardTitle\": \"RCS Apigee testing 4\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/n/n Your Cylinder will be delivered soon.  \",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Visit Now\",\n                                        \"postBack\": {\n                                            \"data\": \"visit_now_election24{{$50}}\"\n                                        },\n                                        \"openUrl\": {\n                                            \"url\": \"https://elections24.eci.gov.in/\"\n                                        }\n                                    }\n                                }\n                            ]\n                        }\n                    ]\n                }\n            }\n        }\n    },\n    \"data_sms\": {\n    \"sender_id\": \"Add Sender ID here\",\n    \"domain_id\": \"Add Domain ID here\",\n    \"sms_type\": \"T\",\n    \"sms_content_type\": \"Static\",\n    \"dlt_entity_id\": \"12054436650\",\n    \"body\": \"SMS Body\",\n    \"dlt_template_id\": \"127191037\"\n    }\n}"}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}], "description": "Carousel with Suggested reply, Calender event, Share Location, Dialer Action and Open URL and SMS"}, {"name": "Carousel Without SMS", "item": [{"name": "Carousel Send Msg", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "youe key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"4a9c72a9-1d13-4030-994f-5cc857603445\",\n    \"agentID\": \"your agent<PERSON>\",\n    \"campaignID\": \"today check\",\n    \"contacts\": [\n        \"+9187619155\"\n        // \"+919321921653\",\n        // \"+919324900790\"\n        // \"+919158710200\",\n        // \"+919867614567\",\n        // \"+918451059612\",\n        // \"7977624341\",\n        // \"+91232323\",\n        // \"+919421464605\"\n    ],\n    \"data\": {\n        \"Content\": {\n            \"richCardDetails\": {\n                \"carousel\": {\n                    \"cardWidth\": \"MEDIUM_WIDTH\",\n                    \"contents\": [\n                        {\n                            \"cardTitle\": \"RCS Infobip Testing\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/nf/n Your Cylinder will be delivered soon.  \",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Pay Now\",\n                                        \"postBack\": {\n                                            \"data\": \"call_back_data_for_button_1_goes_here {{$50}} \"\n                                        },\n                                        \"openUrl\": {\n                                            \"url\": \"https://www.ioclmd.in/\"\n                                        }\n                                    }\n                                }\n                            ]\n                        },\n                        {\n                            \"cardTitle\": \"RCS Apigee testing 2\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/n/n Your Cylinder will be delivered soon.\",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Pay Now\",\n                                        \"postBack\": {\n                                            \"data\": \"call_back_data_for_button_1_goes_here {{$50}} \"\n                                        },\n                                        \"openUrl\": {\n                                            \"url\": \"https://www.ioclmd.in/\"\n                                        }\n                                    }\n                                }\n                            ]\n                        },\n                        {\n                            \"cardTitle\": \"RCS Apigee testing 3\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/n/n Your Cylinder will be delivered soon.  \",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Pay Now\",\n                                        \"postBack\": {\n                                            \"data\": \"call_back_data_for_button_1_goes_here {{$50}} \"\n                                        },\n                                        \"openUrl\": {\n                                            \"url\": \"https://www.ioclmd.in/\"\n                                        }\n                                    }\n                                }\n                            ]\n                        },\n                        {\n                            \"cardTitle\": \"RCS Apigee testing 4\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/n/n Your Cylinder will be delivered soon.  \",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Pay Now\",\n                                        \"postBack\": {\n                                            \"data\": \"call_back_data_for_button_1_goes_here {{$50}} \"\n                                        },\n                                        \"openUrl\": {\n                                            \"url\": \"https://www.ioclmd.in/\"\n                                        }\n                                    }\n                                }\n                            ]\n                        }\n                    ]\n                }\n            }\n        }\n    }\n}"}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}, {"name": "Carousel with Suggested reply, Calender event, Share Location, Dialer Action and Open URL", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-apikey", "value": "your key"}], "body": {"mode": "raw", "raw": "{\n    \"messageID\": \"4a9c72a9-1d13-4030-994f-5cc857603445\",\n    \"agentID\": \"your agentID\",\n    \"campaignID\": \"today check\",\n    \"contacts\": [\n        \"+918719155\"\n        // \"+919321921653\",\n        // \"+919324900790\"\n        // \"+919158710200\",\n        // \"+919867614567\",\n        // \"+918451059612\",\n        // \"7977624341\",\n        // \"+91232323\",\n        // \"+919421464605\"\n    ],\n    \"data\": {\n        \"Content\": {\n            \"richCardDetails\": {\n                \"carousel\": {\n                    \"cardWidth\": \"MEDIUM_WIDTH\",\n                    \"contents\": [\n                        {\n                            \"cardTitle\": \"RCS Infobip Testing\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/nf/n Your Cylinder will be delivered soon.  \",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Mark Your Calendar\",\n                                        \"postBack\": {\n                                            \"data\": \"SA1L1C1{{$50}}\"\n                                        },\n                                        \"createCalendarEvent\": {\n                                            \"startTime\": \"2023-06-26T15:01:23Z\",\n                                            \"endTime\": \"2023-06-26T18:01:23Z\",\n                                            \"title\": \"RCS Seminar\",\n                                            \"description\": \"Session 1 of 4\"\n                                        }\n                                    }\n                                }\n                            ]\n                        },\n                        {\n                            \"cardTitle\": \"RCS Apigee testing 2\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/n/n Your Cylinder will be delivered soon.\",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Visit Now\",\n                                        \"postBack\": {\n                                            \"data\": \"SA1L1C1{{$50}}\"\n                                        },\n                                        \"showLocation\": {\n                                            \"coordinAtes\": {\n                                                \"latitude\": 21.5937,\n                                                \"longitude\": 78.9629\n                                            },\n                                            \"label\": \"Label - Show Location\"\n                                        }\n                                    }\n                                }\n                            ]\n                        },\n                        {\n                            \"cardTitle\": \"RCS Apigee testing 3\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/n/n Your Cylinder will be delivered soon.  \",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Dial Now\",\n                                        \"postBack\": {\n                                            \"data\": \"SA1L1C1{{$50}}\"\n                                        },\n                                        \"dialerAction\": {\n                                            \"phoneNumber\": \"+918779619155\"\n                                        }\n                                    }\n                                }\n                            ]\n                        },\n                        {\n                            \"cardTitle\": \"RCS Apigee testing 4\",\n                            \"cardDescription\": \"Refill Booking no confirmed: 1234567 /n /n Paying for your LPG Cylinder is now quick, easy and hassle free!!/n/n To make instant payment on Amazon Pay, Click on 'Pay Now' below/n/n Your Cylinder will be delivered soon.  \",\n                            \"cardMedia\": {\n                                \"mediaHeight\": \"MEDIUM\",\n                                \"contentInfo\": {\n                                    \"fileUrl\": \"https://jfxv.akamaized.net/Bots/653b6ee89ea38113df3a8b4d/1707202455722.jpg\"\n                                }\n                            },\n                            \"suggestions\": [\n                                {\n                                    \"action\": {\n                                        \"plainText\": \"Visit Now\",\n                                        \"postBack\": {\n                                            \"data\": \"visit_now_election24{{$50}}\"\n                                        },\n                                        \"openUrl\": {\n                                            \"url\": \"https://elections24.eci.gov.in/\"\n                                        }\n                                    }\n                                }\n                            ]\n                        }\n                    ]\n                }\n            }\n        }\n    }\n}"}, "url": "https://rcsapi.jiocx.com/api/v1/sendMessage"}, "response": []}]}], "description": "Carousel with Suggested reply, Calender event, Share Location, Dialer Action and Open URL"}, {"name": "Capability", "item": [{"name": "File Upload", "item": [{"name": "File Upload", "request": {"method": "POST", "header": [{"key": "x-apikey", "value": "Your Agent Key"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/C:/Users/<USER>/Downloads/1111 (3) (1).png"}, {"key": "agentId", "value": "Your Agent ID", "type": "text"}]}, "url": "https://rcsapi.jiocx.com/api/v1/uploadFile"}, "response": []}]}, {"name": "Check Capability", "request": {"method": "POST", "header": [{"key": "x-apikey", "value": "Your Agent Key"}, {"key": "agentid", "value": "Your Agent ID"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\"PhoneNumbers\":\"+919167464046\" \n}"}, "url": "https://rcsapi.jiocx.com/api/v1/checkCapability"}, "response": []}]}]}