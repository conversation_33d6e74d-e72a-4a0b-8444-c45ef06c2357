<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Jobs\SendTaskJob;
use App\Models\User;
use Auth;
use Illuminate\Support\Facades\Log;

class BulkInsertTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $data, $numbers;
    /**
     * Create a new job instance.
     */
    public function __construct($data, $numbers)
    {
        $this->data = $data;
        $this->numbers = $numbers;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Define the batch size (10,000 entries per batch)
        $batchSize = 1000;
        $numberBatches = array_chunk($this->numbers, $batchSize);
        $whatsapp_ids = [];

        $user = User::where('id', $this->data['created_by'])->first();
        $costPerMessage = $user->business_initiated;

        foreach ($numberBatches as $numberBatch) {
            $taskdata = [];

            foreach ($numberBatch as $number) {
                // Check if user has enough balance for this message
                $insufficientBalance = $user->balance < $costPerMessage;

                $uuid = (string) Str::uuid();
                $whatsapp_ids[] = [
                    'uuid' => $uuid,
                    'can_send' => !$insufficientBalance
                ];

                $taskdata[] = [
                    'device_id' => $this->data['device_id'],
                    'created_by' => $this->data['created_by'],
                    'launched_on' => $this->data['launched_on'],
                    'scheduled_on' => $this->data['scheduled_on'],
                    'task_url' => $this->data['task_url'],
                    'reply_message_id' => $this->data['reply_message_id'] ?? null,
                    'campaign_name' => $this->data['campaign_name'] ?? null,
                    'templateId' => $this->data['templateId'],
                    'language' => $this->data['language'],
                    'is_reply' => $this->data['is_reply'] ?? '0',
                    'task_type' => $this->data['task_type'],
                    'parameters' => $this->data['parameters'] ?? null,
                    'buttons' => $this->data['buttons'] ?? null,
                    // 'flow_id' => $this->data['flow_id'] ?? null,
                    'send_to_number' => $number,
                    'text' => $this->data['text'],
                    'ip' => $this->data['ip'],
                    'task_status' => $insufficientBalance ? 4 : 0,
                    'task_description' => $insufficientBalance ? 'Your account balance is insufficient. Please top up your balance.' : null,
                    'whatsapp_id' => $uuid,
                    'created_at' => now(),
                    'updated_at' => now()
                ];
                // dd($taskdata);

                // If balance is sufficient, deduct the cost
                if (!$insufficientBalance) {
                    DB::transaction(function () use ($user, $costPerMessage) {
                        $user->balance -= $costPerMessage;
                        $user->save();
                    });
                }
            }
            try {
                DB::table('task')->insert($taskdata);
            } catch (\Exception $e) {
                Log::error('Error inserting task batch: ' . $e->getMessage(), [
                    'error' => $e->getTraceAsString()
                ]);
                continue;
            }
        }

        // send message on sufficient balance
        foreach ($whatsapp_ids as $waidData) {
            if ($waidData['can_send']) {
                try {
                    if (count($this->numbers) < 10) {
                        dispatch(new SendTaskJob($waidData['uuid']))
                            ->delay(now()->setTimeFromTimeString($this->data['scheduled_on']))
                            ->onQueue('high');
                    } else {
                        dispatch(new SendTaskJob($waidData['uuid']))
                            ->delay(now()->setTimeFromTimeString($this->data['scheduled_on']));
                    }
                } catch (\Exception $e) {
                    Log::error('Error dispatching SendTaskJob: ' . $e->getMessage(), [
                        'whatsapp_id' => $waidData['uuid'],
                        'error' => $e->getTraceAsString()
                    ]);

                    // Update task status to failed if job dispatch fails
                    DB::table('task')
                        ->where('whatsapp_id', $waidData['uuid'])
                        ->update([
                            'task_status' => 4,
                            'task_description' => 'Failed to queue message: ' . $e->getMessage()
                        ]);
                }
            }
        }
    }
}
